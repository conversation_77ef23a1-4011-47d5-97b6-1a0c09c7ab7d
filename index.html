<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>HumOS Canvas - Your Personal AI Integration Framework</title>
    <meta name="title" content="HumOS Canvas - Your Personal AI Integration Framework">
    <meta name="description" content="HumOS Canvas: Your Personal Interface for Enhanced AI Collaboration. Create, connect, and explore ideas with AI-powered mind mapping and infinite canvas visualization.">
    <meta name="keywords" content="AI collaboration, mind mapping, infinite canvas, HumOS, AI integration, visual thinking, idea generation, knowledge management">
    <meta name="author" content="HumOS">
    <meta name="robots" content="index, follow">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="dist/favicon.svg">
    <link rel="icon" type="image/svg+xml" href="public/humos-icon.svg" sizes="any">
    <link rel="apple-touch-icon" href="public/humos-icon.svg">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://humos-canvas.vercel.app/">
    <meta property="og:title" content="HumOS Canvas - Your Personal AI Integration Framework">
    <meta property="og:description" content="HumOS Canvas: Your Personal Interface for Enhanced AI Collaboration. Create, connect, and explore ideas with AI-powered mind mapping and infinite canvas visualization.">
    <meta property="og:image" content="public/image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="image/png">
    <meta property="og:site_name" content="HumOS Canvas">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://humos-canvas.vercel.app/">
    <meta property="twitter:title" content="HumOS Canvas - Your Personal AI Integration Framework">
    <meta property="twitter:description" content="HumOS Canvas: Your Personal Interface for Enhanced AI Collaboration. Create, connect, and explore ideas with AI-powered mind mapping and infinite canvas visualization.">
    <meta property="twitter:image" content="public/image.png">
    <meta property="twitter:image:alt" content="HumOS Canvas - AI-powered mind mapping interface">

    <!-- Additional SEO -->
    <meta name="theme-color" content="#1a1a1a">
    <meta name="msapplication-TileColor" content="#1a1a1a">
    <meta name="application-name" content="HumOS Canvas">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "HumOS Canvas",
      "description": "Your Personal Interface for Enhanced AI Collaboration. Create, connect, and explore ideas with AI-powered mind mapping and infinite canvas visualization.",
      "url": "https://humos-canvas.vercel.app/",
      "applicationCategory": "ProductivityApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "creator": {
        "@type": "Organization",
        "name": "HumOS"
      }
    }
    </script>

    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- Canvas element for drawing -->
        <canvas id="canvas"></canvas>
        

        <!-- Floating toolbar -->
        <div class="floating-toolbar">
            <button id="addNodeBtn" class="toolbar-btn">Add Node</button>
            <button id="exportBtn" class="toolbar-btn">Export JSON</button>
            <button id="importBtn" class="toolbar-btn">Import JSON</button>
            <button id="configureApiBtn" class="toolbar-btn">🔑 Configure API Keys</button>
        </div>

        <!-- Hidden file input for import -->
        <input type="file" id="importFileInput" accept=".json" style="display: none;">

        <!-- Generate Ideas tooltip (appears above selected nodes) -->
        <div id="generateIdeasTooltip" class="generate-ideas-tooltip hidden">
            <button id="generateIdeasBtn" class="generate-ideas-btn" title="Generate Ideas">🚀🤖</button>
            <button id="expandContentBtn" class="expand-content-btn" title="Expand Content">⛶</button>
        </div>

        <!-- Content Expansion Modal -->
        <div id="contentModal" class="content-modal hidden">
            <div class="modal-backdrop"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h3 id="modalTitle">Node Content</h3>
                    <button id="closeModalBtn" class="close-modal-btn">×</button>
                </div>
                <div class="modal-body">
                    <div id="modalContent" class="modal-content-text"></div>
                </div>
            </div>
        </div>

        <!-- Selection status indicator -->
        <div id="selectionStatus" class="selection-status hidden">
            <span id="selectionCount">0</span> nodes selected
            <small id="selectionHint" class="selection-hint hidden">Select a single node to generate AI ideas</small>
        </div>
        
        <!-- Canvas navigation controls -->
        <div class="canvas-controls">
            <button id="zoomInBtn" class="control-btn">+</button>
            <button id="zoomOutBtn" class="control-btn">-</button>
            <button id="resetViewBtn" class="control-btn">⌂</button>
            <div class="control-separator"></div>
            <button id="undoBtn" class="control-btn" title="Undo">↶</button>
            <button id="redoBtn" class="control-btn" title="Redo">↷</button>
            <div class="control-separator"></div>
            <button id="fontSizeDecreaseBtn" class="control-btn" title="Decrease Font Size">A-</button>
            <button id="fontSizeIncreaseBtn" class="control-btn" title="Increase Font Size">A+</button>
        </div>

        <!-- Navigation help -->
        <div class="navigation-help">
            <div class="help-item">🖱️ Right-click + drag to pan</div>
            <div class="help-item">⌨️ Space + drag to pan</div>
            <div class="help-item">👆 Two-finger scroll to pan</div>
            <div class="help-item">🤏 Pinch to zoom</div>
            <div class="help-item">🖱️ Scroll to zoom</div>
        </div>
    </div>
    
    <script type="module" src="main.js"></script>
</body>
</html>